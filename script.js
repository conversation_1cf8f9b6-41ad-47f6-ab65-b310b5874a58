// PDF Page Rotator - iLovePDF Style
class PDFRotator {
    constructor() {
        this.pdfDoc = null;
        this.pdfFile = null;
        this.pageRotations = new Map(); // Store rotation for each page
        this.selectedPages = new Set(); // Store selected pages
        this.currentRotationAngle = 90;
        this.isProcessing = false; // Prevent multiple file processing
        this.uploadClickHandler = null;
        this.uploadDropHandler = null;

        this.init();
    }

    init() {
        this.bindElements();
        this.attachEventListeners();
        this.setupPDFJS();
    }

    setupPDFJS() {
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js';
        }
    }

    bindElements() {
        // Main areas
        this.uploadArea = document.getElementById('uploadArea');
        this.previewArea = document.getElementById('previewArea');
        this.toolHeader = document.getElementById('toolHeader');
        
        // Upload elements
        this.fileInput = document.getElementById('fileInput');
        this.selectBtn = document.getElementById('selectBtn');
        
        // File info
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        this.removeBtn = document.getElementById('removeBtn');
        
        // Pages container
        this.pagesContainer = document.getElementById('pagesContainer');
        
        // Controls
        this.rotateButtons = document.querySelectorAll('.btn-rotate');
        this.resetBtn = document.getElementById('resetBtn');
        this.rotateBtn = document.getElementById('rotateBtn');
        
        // Overlays and modals
        this.processingOverlay = document.getElementById('processingOverlay');
        this.progressFill = document.getElementById('progressFill');
        this.processingText = document.getElementById('processingText');
        this.processingTitle = document.getElementById('processingTitle');
        
        this.downloadModal = document.getElementById('downloadModal');
        this.downloadBtn = document.getElementById('downloadBtn');
        this.newFileBtn = document.getElementById('newFileBtn');
        
        // Toast container
        this.toastContainer = document.getElementById('toastContainer');
    }

    attachEventListeners() {
        // File selection
        this.selectBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.removeBtn.addEventListener('click', () => this.removeFile());
        
        // Drag and drop
        this.setupDragAndDrop();
        
        // Rotation controls
        this.rotateButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.selectRotationAngle(e));
        });
        
        // Action buttons
        this.resetBtn.addEventListener('click', () => this.resetAll());
        this.rotateBtn.addEventListener('click', () => this.processRotation());
        
        // Modal controls
        this.newFileBtn.addEventListener('click', () => this.startNew());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    setupDragAndDrop() {
        // Remove any existing event listeners to prevent duplicates
        this.uploadArea.removeEventListener('click', this.uploadClickHandler);
        this.uploadArea.removeEventListener('drop', this.uploadDropHandler);

        // Bind handlers to maintain context
        this.uploadClickHandler = () => this.fileInput.click();
        this.uploadDropHandler = (e) => {
            const files = e.dataTransfer.files;
            this.handleFiles(files);
        };

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            this.uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            this.uploadArea.addEventListener(eventName, () => {
                this.uploadArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            this.uploadArea.addEventListener(eventName, () => {
                this.uploadArea.classList.remove('dragover');
            }, false);
        });

        this.uploadArea.addEventListener('drop', this.uploadDropHandler, false);

        // Click to upload - only add if not already processing a file
        if (!this.pdfFile) {
            this.uploadArea.addEventListener('click', this.uploadClickHandler);
        }
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    handleFileSelect(e) {
        const files = e.target.files;
        this.handleFiles(files);
        e.target.value = '';
    }

    async handleFiles(files) {
        if (files.length === 0) return;

        // Prevent multiple file processing
        if (this.isProcessing) {
            this.showToast('جارٍ معالجة ملف آخر، يرجى الانتظار', 'warning');
            return;
        }

        const file = files[0];

        if (file.type !== 'application/pdf') {
            this.showToast('يرجى اختيار ملف PDF صالح فقط', 'error');
            return;
        }

        if (file.size > 50 * 1024 * 1024) { // 50MB limit
            this.showToast('حجم الملف كبير جداً (أكثر من 50 ميجابايت)', 'error');
            return;
        }

        // If there's already a file loaded, ask for confirmation
        if (this.pdfFile) {
            if (!confirm('هل تريد استبدال الملف الحالي؟')) {
                return;
            }
        }

        this.pdfFile = file;
        this.isProcessing = true;
        await this.loadPDF();
        this.isProcessing = false;
    }

    async loadPDF() {
        try {
            this.showProcessing('جارٍ تحميل الملف...');
            
            // Load PDF with pdf-lib for processing
            const arrayBuffer = await this.pdfFile.arrayBuffer();
            this.pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
            
            // Update file info
            this.updateFileInfo();
            
            // Generate page thumbnails
            await this.generatePageThumbnails();
            
            // Show preview area
            this.showPreviewArea();
            this.hideProcessing();
            
            this.showToast(`تم تحميل الملف بنجاح (${this.pdfDoc.getPageCount()} صفحة)`, 'success');
            
        } catch (error) {
            console.error('Error loading PDF:', error);
            this.showToast('فشل في تحميل ملف PDF. يرجى التحقق من الملف.', 'error');
            this.hideProcessing();
        }
    }

    updateFileInfo() {
        this.fileName.textContent = this.pdfFile.name;
        this.fileSize.textContent = this.formatFileSize(this.pdfFile.size) + ' • ' + this.pdfDoc.getPageCount() + ' صفحة';
    }

    async generatePageThumbnails() {
        try {
            const arrayBuffer = await this.pdfFile.arrayBuffer();
            const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
            const pageCount = pdf.numPages;
            
            this.pagesContainer.innerHTML = '';
            
            // Add select all button
            const selectAllContainer = document.createElement('div');
            selectAllContainer.style.gridColumn = '1 / -1';
            selectAllContainer.style.textAlign = 'center';
            selectAllContainer.style.marginBottom = '10px';
            
            const selectAllBtn = document.createElement('button');
            selectAllBtn.className = 'select-all-btn';
            selectAllBtn.textContent = 'تحديد الكل';
            selectAllBtn.addEventListener('click', () => this.selectAllPages());
            
            selectAllContainer.appendChild(selectAllBtn);
            this.pagesContainer.appendChild(selectAllContainer);
            
            // Generate thumbnails for each page
            for (let pageNum = 1; pageNum <= pageCount; pageNum++) {
                await this.createPageThumbnail(pdf, pageNum);
                
                // Update progress
                const progress = (pageNum / pageCount) * 100;
                this.updateProgress(progress, `جارٍ إنشاء الصور المصغرة... ${pageNum}/${pageCount}`);
            }
            
        } catch (error) {
            console.error('Error generating thumbnails:', error);
            this.showToast('خطأ في إنشاء الصور المصغرة', 'error');
        }
    }

    async createPageThumbnail(pdf, pageNum) {
        const page = await pdf.getPage(pageNum);
        
        // Create page item container
        const pageItem = document.createElement('div');
        pageItem.className = 'page-item';
        pageItem.dataset.pageNum = pageNum;
        
        // Create canvas for thumbnail
        const canvas = document.createElement('canvas');
        canvas.className = 'page-canvas';
        const context = canvas.getContext('2d');
        
        // Calculate scale for thumbnail
        const viewport = page.getViewport({ scale: 1 });
        const scale = Math.min(150 / viewport.width, 200 / viewport.height);
        const scaledViewport = page.getViewport({ scale });
        
        canvas.width = scaledViewport.width;
        canvas.height = scaledViewport.height;
        
        // Render page
        const renderContext = {
            canvasContext: context,
            viewport: scaledViewport
        };
        
        await page.render(renderContext).promise;
        
        // Create page number label
        const pageNumber = document.createElement('div');
        pageNumber.className = 'page-number';
        pageNumber.textContent = `صفحة ${pageNum}`;
        
        // Create rotation indicator
        const rotationIndicator = document.createElement('div');
        rotationIndicator.className = 'page-rotation';
        rotationIndicator.textContent = '0°';
        
        // Add click event for page selection
        pageItem.addEventListener('click', () => this.togglePageSelection(pageNum));
        
        // Assemble page item
        pageItem.appendChild(canvas);
        pageItem.appendChild(pageNumber);
        pageItem.appendChild(rotationIndicator);
        
        this.pagesContainer.appendChild(pageItem);
        
        // Initialize rotation
        this.pageRotations.set(pageNum, 0);
    }

    togglePageSelection(pageNum) {
        const pageItem = document.querySelector(`[data-page-num="${pageNum}"]`);
        
        if (this.selectedPages.has(pageNum)) {
            this.selectedPages.delete(pageNum);
            pageItem.classList.remove('selected');
        } else {
            this.selectedPages.add(pageNum);
            pageItem.classList.add('selected');
        }
        
        this.updateRotateButtonState();
    }

    selectAllPages() {
        const pageCount = this.pdfDoc.getPageCount();
        
        if (this.selectedPages.size === pageCount) {
            // Deselect all
            this.selectedPages.clear();
            document.querySelectorAll('.page-item').forEach(item => {
                item.classList.remove('selected');
            });
            document.querySelector('.select-all-btn').textContent = 'تحديد الكل';
        } else {
            // Select all
            this.selectedPages.clear();
            for (let i = 1; i <= pageCount; i++) {
                this.selectedPages.add(i);
            }
            document.querySelectorAll('.page-item').forEach(item => {
                item.classList.add('selected');
            });
            document.querySelector('.select-all-btn').textContent = 'إلغاء تحديد الكل';
        }
        
        this.updateRotateButtonState();
    }

    selectRotationAngle(e) {
        // Remove active class from all buttons
        this.rotateButtons.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to clicked button
        e.currentTarget.classList.add('active');
        
        // Set rotation angle
        this.currentRotationAngle = parseInt(e.currentTarget.dataset.angle);
        
        // Apply rotation to selected pages preview
        this.applyPreviewRotation();
    }

    applyPreviewRotation() {
        this.selectedPages.forEach(pageNum => {
            const pageItem = document.querySelector(`[data-page-num="${pageNum}"]`);
            const canvas = pageItem.querySelector('.page-canvas');
            const rotationIndicator = pageItem.querySelector('.page-rotation');
            
            if (pageItem) {
                const currentRotation = this.pageRotations.get(pageNum) || 0;
                const newRotation = (currentRotation + this.currentRotationAngle) % 360;
                
                canvas.style.transform = `rotate(${newRotation}deg)`;
                rotationIndicator.textContent = `${newRotation}°`;
                
                if (newRotation !== 0) {
                    pageItem.classList.add('rotated');
                } else {
                    pageItem.classList.remove('rotated');
                }
                
                this.pageRotations.set(pageNum, newRotation);
            }
        });
    }

    updateRotateButtonState() {
        const hasSelection = this.selectedPages.size > 0;
        this.rotateBtn.disabled = !hasSelection;
        
        if (hasSelection) {
            this.rotateBtn.textContent = `تدوير ${this.selectedPages.size} صفحة`;
        } else {
            this.rotateBtn.textContent = 'تدوير الملف';
        }
    }

    async processRotation() {
        if (this.selectedPages.size === 0) {
            this.showToast('يرجى تحديد الصفحات للتدوير', 'warning');
            return;
        }
        
        try {
            this.showProcessing('جارٍ تدوير الصفحات...');
            
            // Create a copy of the PDF document
            const pdfDocCopy = await PDFLib.PDFDocument.load(await this.pdfFile.arrayBuffer());
            const pages = pdfDocCopy.getPages();
            
            // Apply rotations to selected pages
            let rotatedCount = 0;
            this.selectedPages.forEach(pageNum => {
                const pageIndex = pageNum - 1;
                const rotation = this.pageRotations.get(pageNum) || 0;
                
                if (rotation > 0 && pages[pageIndex]) {
                    pages[pageIndex].setRotation(PDFLib.degrees(rotation));
                    rotatedCount++;
                }
                
                // Update progress
                const progress = (rotatedCount / this.selectedPages.size) * 100;
                this.updateProgress(progress, `جارٍ تدوير الصفحة ${rotatedCount}/${this.selectedPages.size}`);
            });
            
            // Generate PDF bytes
            const pdfBytes = await pdfDocCopy.save();
            
            // Create download blob
            const blob = new Blob([pdfBytes], { type: 'application/pdf' });
            const url = URL.createObjectURL(blob);
            
            // Set download link
            this.downloadBtn.href = url;
            this.downloadBtn.download = this.generateFileName();
            
            this.hideProcessing();
            this.showDownloadModal();
            
        } catch (error) {
            console.error('Error processing rotation:', error);
            this.showToast('خطأ في تدوير الملف', 'error');
            this.hideProcessing();
        }
    }

    generateFileName() {
        const originalName = this.pdfFile.name.replace('.pdf', '');
        return `${originalName}_rotated.pdf`;
    }

    removeFile() {
        this.resetAll();
        this.showUploadArea();
        this.showToast('تم حذف الملف', 'success');
    }

    resetAll() {
        this.pdfDoc = null;
        this.pdfFile = null;
        this.pageRotations.clear();
        this.selectedPages.clear();
        this.fileInput.value = '';
        this.isProcessing = false;

        // Reset rotation buttons
        this.rotateButtons.forEach(btn => btn.classList.remove('active'));
        this.rotateButtons[0].classList.add('active');
        this.currentRotationAngle = 90;

        // Clear pages container
        this.pagesContainer.innerHTML = '';

        // Re-enable upload area click handler
        this.setupDragAndDrop();
    }

    startNew() {
        this.hideDownloadModal();
        this.resetAll();
        this.showUploadArea();
    }

    showUploadArea() {
        this.toolHeader.style.display = 'flex';
        this.uploadArea.style.display = 'flex';
        this.previewArea.style.display = 'none';
    }

    showPreviewArea() {
        this.toolHeader.style.display = 'none';
        this.uploadArea.style.display = 'none';
        this.previewArea.style.display = 'flex';

        // Remove upload area click handler when file is loaded
        this.uploadArea.removeEventListener('click', this.uploadClickHandler);
    }

    showProcessing(title = 'جارٍ المعالجة...') {
        this.processingTitle.textContent = title;
        this.updateProgress(0, 'جارٍ البدء...');
        this.processingOverlay.style.display = 'flex';
    }

    hideProcessing() {
        this.processingOverlay.style.display = 'none';
    }

    updateProgress(percent, text) {
        this.progressFill.style.width = `${percent}%`;
        this.processingText.textContent = `${Math.round(percent)}%`;
        
        if (text) {
            this.processingTitle.textContent = text;
        }
    }

    showDownloadModal() {
        this.downloadModal.style.display = 'flex';
    }

    hideDownloadModal() {
        this.downloadModal.style.display = 'none';
    }

    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icon = document.createElement('i');
        icon.className = 'fas toast-icon';
        
        switch (type) {
            case 'success':
                icon.classList.add('fa-check-circle');
                break;
            case 'error':
                icon.classList.add('fa-exclamation-circle');
                break;
            case 'warning':
                icon.classList.add('fa-exclamation-triangle');
                break;
        }
        
        const messageSpan = document.createElement('span');
        messageSpan.className = 'toast-message';
        messageSpan.textContent = message;
        
        toast.appendChild(icon);
        toast.appendChild(messageSpan);
        
        this.toastContainer.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    handleKeyboard(e) {
        // Ctrl+A to select all pages
        if (e.ctrlKey && e.key === 'a' && this.previewArea.style.display === 'flex') {
            e.preventDefault();
            this.selectAllPages();
        }
        
        // Delete key to remove file
        if (e.key === 'Delete' && this.previewArea.style.display === 'flex') {
            this.removeFile();
        }
        
        // Enter to process rotation
        if (e.key === 'Enter' && this.selectedPages.size > 0) {
            this.processRotation();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PDFRotator();
});

// Handle modal close on background click
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('processing-overlay') || 
        e.target.classList.contains('download-modal')) {
        // Don't close processing overlay by clicking background
        if (e.target.classList.contains('download-modal')) {
            document.getElementById('downloadModal').style.display = 'none';
        }
    }
});