# مدوّر صفحات PDF - PDF Page Rotator

تطبيق ويب حديث ومتطور لتدوير صفحات ملفات PDF مع معاينة مباشرة وخيارات متقدمة للتحكم.

## الميزات الجديدة والمحسنة

### 🎨 التصميم والواجهة
- **تصميم حديث وجذاب** مع خلفيات متدرجة وتأثيرات بصرية متقدمة
- **واجهة مستخدم باللغة العربية** مع دعم كامل للنصوص من اليمين إلى اليسار
- **تصميم متجاوب** يعمل بشكل مثالي على جميع الأجهزة (الهواتف، الأجهزة اللوحية، أجهزة الكمبيوتر)
- **تأثيرات حركية سلسة** مع انتقالات ناعمة وتفاعلات بصرية
- **أيقونات Font Awesome** لتحسين التجربة البصرية
- **أيقونة متحركة** في الرأس تدور باستمرار

### 🚀 الوظائف المحسنة
- **سحب وإفلات محسن** مع تأثيرات بصرية واضحة
- **معاينة مباشرة** للصفحة الأولى من ملف PDF
- **تحكم في التدوير المرئي** للمعاينة قبل التطبيق
- **خيارات تدوير متقدمة**:
  - جميع الصفحات
  - نطاق صفحات محدد
  - صفحات محددة بالأرقام (مع دعم النطاقات)
- **زوايا تدوير مرنة**: 90°، 180°، 270°، أو زاوية مخصصة
- **معلومات تفصيلية** عن الملف (الاسم، الحجم، عدد الصفحات)

### 📊 تحسينات الأداء
- **معالجة ملفات محسنة** مع دعم ملفات كبيرة (حتى 50 ميجابايت)
- **مؤشر تقدم مفصل** أثناء المعالجة
- **تنبيهات ذكية** (Toast Notifications) لتحديثات الحالة
- **معاينة سريعة** باستخدام PDF.js
- **إدارة ذاكرة محسنة** لمعالجة ملفات PDF الكبيرة

### 🔧 الميزات التقنية
- **كود JavaScript منظم** باستخدام الفئات (Classes)
- **معالجة أخطاء شاملة** مع رسائل واضحة للمستخدم
- **مكتبات حديثة**: PDF-lib و PDF.js
- **إمكانية الوصول المحسنة** (Accessibility) مع دعم لوحة المفاتيح
- **متغيرات CSS** لسهولة التخصيص والصيانة
- **نافذة تنزيل تفاعلية** مع إحصائيات العملية

## الملفات المضمنة

1. **index.html** - الهيكل الأساسي للتطبيق
2. **style.css** - التصميم والأنماط المتقدمة
3. **script.js** - المنطق والوظائف المحسنة
4. **README.md** - دليل الاستخدام والتوثيق

## كيفية الاستخدام

1. **افتح ملف `index.html`** في أي متصفح حديث
2. **اسحب وأفلت ملف PDF** أو انقر على "اختيار ملف PDF"
3. **اعرض المعاينة** وجرب التدوير المرئي
4. **اضبط إعدادات التدوير**:
   - اختر نطاق الصفحات (جميع الصفحات، نطاق محدد، أو صفحات محددة)
   - حدد زاوية التدوير (90°، 180°، 270°، أو زاوية مخصصة)
5. **انقر على "تدوير PDF"** لبدء المعالجة
6. **نزّل الملف المدور** من النافذة المنبثقة

## المتطلبات

- متصفح حديث يدعم HTML5 و CSS3 و JavaScript ES6+
- اتصال بالإنترنت لتحميل المكتبات الخارجية:
  - PDF-lib (لمعالجة وتدوير PDF)
  - PDF.js (لمعاينة PDF)
  - Font Awesome (للأيقونات)

## التحسينات المقارنة بالإصدار السابق

### التصميم
- ✅ تصميم حديث مع خلفيات متدرجة وتأثيرات بصرية
- ✅ واجهة عربية كاملة مع دعم RTL
- ✅ تأثيرات حركية متقدمة
- ✅ تصميم متجاوب محسن

### الوظائف
- ✅ معاينة مباشرة للصفحة الأولى من PDF
- ✅ تحكم مرئي في التدوير قبل التطبيق
- ✅ خيارات تدوير متقدمة (صفحات محددة، نطاقات)
- ✅ زوايا تدوير مرنة ومخصصة
- ✅ معلومات تفصيلية عن الملف
- ✅ نافذة تنزيل تفاعلية مع إحصائيات

### الأداء
- ✅ كود منظم وقابل للصيانة
- ✅ معالجة ملفات محسنة
- ✅ مؤشر تقدم مفصل
- ✅ تنبيهات ذكية
- ✅ إدارة ذاكرة أفضل

## الدعم والتوافق

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ أجهزة الهواتف الذكية
- ✅ الأجهزة اللوحية

## ملاحظات مهمة

- يتم معالجة جميع ملفات PDF محلياً في المتصفح (لا يتم رفعها لأي خادم)
- الحد الأقصى لحجم ملف PDF: 50 ميجابايت
- يدعم التطبيق جميع أنواع ملفات PDF
- جميع الإعدادات محفوظة محلياً أثناء الجلسة
- يمكن تدوير الصفحات بزوايا مخصصة (مضاعفات 90 درجة)

## الميزات المتقدمة

### خيارات نطاق الصفحات
- **جميع الصفحات**: تطبيق التدوير على كامل المستند
- **نطاق محدد**: تحديد صفحة البداية والنهاية
- **صفحات محددة**: تحديد صفحات بالأرقام مع دعم النطاقات (مثل: 1,3,5-8,10)

### زوايا التدوير
- **90 درجة**: تدوير ربع دورة (الافتراضي)
- **180 درجة**: تدوير نصف دورة (قلب الصفحة)
- **270 درجة**: تدوير ثلاثة أرباع دورة
- **زاوية مخصصة**: إدخال أي زاوية (يجب أن تكون مضاعف 90)

### معاينة تفاعلية
- عرض الصفحة الأولى من PDF
- تدوير مرئي للمعاينة
- مؤشر زاوية التدوير الحالية
- أزرار تدوير سريعة (يسار/يمين)

---

**تم تطوير هذا التطبيق بواسطة Manus AI مع التركيز على الأداء والتجربة المستخدم المتميزة.**

