<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="PDF Page Rotator App - Easily rotate PDF pages with live preview">
    <meta name="keywords" content="Rotate PDF, Edit PDF, PDF Page Rotation">
    <title>مدوّر صفحات PDF</title>
    
    <!-- External Libraries -->
    <script src="https://unpkg.com/pdf-lib/dist/pdf-lib.min.js"></script>
    <script src="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.min.js"></script>
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Main Container -->
    <div class="main-container">
        <!-- Tool Header -->
        <div class="tool-header" id="toolHeader">
            <div class="tool-icon">
                <i class="fas fa-sync-alt"></i>
            </div>
            <div class="tool-info">
                <h1 class="tool-title">مدوّر صفحات PDF</h1>
                <p class="tool-description">قم بتدوير صفحات ملف PDF بسهولة واختر الصفحات التي تريد تدويرها</p>
            </div>
        </div>

        <div class="upload-area" id="uploadArea">
            <div class="upload-content" id="uploadContent">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <h2>اختر ملف PDF</h2>
                <p>أو اسحب الملف وأسقطه هنا</p>
                <input type="file" id="fileInput" accept="application/pdf" hidden>
                <button class="btn-primary" id="selectBtn">اختر ملف PDF</button>
            </div>
        </div>

        <!-- File Preview Area -->
        <div class="preview-area" id="previewArea" style="display: none;">
            <div class="file-header">
                <div class="file-info">
                    <i class="fas fa-file-pdf"></i>
                    <div>
                        <div class="file-name" id="fileName"></div>
                        <div class="file-size" id="fileSize"></div>
                    </div>
                </div>
                <button class="btn-close" id="removeBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="pages-container" id="pagesContainer">
                <!-- PDF pages will be generated here -->
            </div>

            <div class="controls-area">
                <div class="rotation-controls">
                    <button class="btn-rotate" data-angle="90">
                        <i class="fas fa-redo"></i>
                        <span>تدوير 90°</span>
                    </button>
                    <button class="btn-rotate" data-angle="180">
                        <i class="fas fa-sync"></i>
                        <span>تدوير 180°</span>
                    </button>
                    <button class="btn-rotate" data-angle="270">
                        <i class="fas fa-undo"></i>
                        <span>تدوير 270°</span>
                    </button>
                </div>
                
                <div class="action-controls">
                    <button class="btn-secondary" id="resetBtn">إعادة تعيين</button>
                    <button class="btn-success" id="rotateBtn">تدوير الملف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Processing Overlay -->
    <div class="processing-overlay" id="processingOverlay">
        <div class="processing-content">
            <div class="spinner"></div>
            <h3 id="processingTitle">جارٍ معالجة الملف...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="processingText">0%</p>
        </div>
    </div>

    <!-- Download Modal -->
    <div class="download-modal" id="downloadModal">
        <div class="modal-content">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>تم تدوير الملف بنجاح!</h3>
            <p>يمكنك الآن تحميل الملف المدوّر</p>
            <div class="modal-actions">
                <button class="btn-secondary" id="newFileBtn">ملف جديد</button>
                <a class="btn-success" id="downloadBtn" href="#" download="rotated.pdf">
                    <i class="fas fa-download"></i>
                    تحميل الملف
                </a>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>

