/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Arabic RTL Support */
[dir="rtl"] {
    text-align: right;
}

/* Body and Background */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    line-height: 1.6;
}

/* Main Container */
.main-container {
    width: 1190px;
    height: 850px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 40px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* Tool Header */
.tool-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.tool-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    position: relative;
    overflow: hidden;
}

.tool-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    border-radius: 12px;
    animation: shimmer 3s infinite;
}

.tool-icon i {
    position: relative;
    z-index: 1;
    animation: rotate-icon 4s linear infinite;
}

@keyframes rotate-icon {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.tool-info {
    flex: 1;
}

.tool-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.tool-description {
    font-size: 1rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

/* Upload Area */
.upload-area {
    width: 600px;
    height: 395px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    margin: 0 auto;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #dc2626;
    background: #fff5f5;
}

.upload-content {
    text-align: center;
    padding: 60px 40px;
}

.upload-icon {
    font-size: 4rem;
    color: #dc2626;
    margin-bottom: 20px;
}

.upload-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.upload-content p {
    color: #666;
    margin-bottom: 30px;
    font-size: 1rem;
}

/* Buttons */
.btn-primary {
    background: #dc2626;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #b91c1c;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-success {
    background: #10b981;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-1px);
}

.btn-close {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-close:hover {
    background: #f3f4f6;
    color: #374151;
}

/* Preview Area */
.preview-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-info i {
    font-size: 1.5rem;
    color: #dc2626;
}

.file-name {
    font-weight: 600;
    color: #111827;
    font-size: 1rem;
}

.file-size {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Pages Container */
.pages-container {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px;
    background: #f9fafb;
    border-radius: 8px;
    overflow-y: auto;
    max-height: 500px;
}

.page-item {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.page-item:hover {
    border-color: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.page-item.selected {
    border-color: #dc2626;
    background: #fff5f5;
}

.page-canvas {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.page-number {
    margin-top: 10px;
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.page-rotation {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    display: none;
}

.page-item.rotated .page-rotation {
    display: block;
}

/* Controls Area */
.controls-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #e5e7eb;
    gap: 20px;
    flex-wrap: wrap;
}

.rotation-controls {
    display: flex;
    gap: 10px;
}

.btn-rotate {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    color: #374151;
}

.btn-rotate:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.btn-rotate.active {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
}

.btn-rotate i {
    font-size: 1.2rem;
}

.action-controls {
    display: flex;
    gap: 12px;
}

/* Processing Overlay */
.processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.processing-content {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #dc2626;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.processing-content h3 {
    color: #111827;
    margin-bottom: 20px;
    font-size: 1.25rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f3f4f6;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: #dc2626;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.processing-content p {
    color: #6b7280;
    font-size: 1rem;
}

/* Download Modal */
.download-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    max-width: 450px;
    width: 90%;
}

.success-icon {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 20px;
}

.modal-content h3 {
    color: #111827;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.modal-content p {
    color: #6b7280;
    margin-bottom: 30px;
    font-size: 1rem;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #10b981;
    display: flex;
    align-items: center;
    gap: 12px;
    max-width: 350px;
    animation: slideIn 0.3s ease;
}

.toast.error {
    border-left-color: #dc2626;
}

.toast.warning {
    border-left-color: #f59e0b;
}

.toast-icon {
    font-size: 1.2rem;
    color: #10b981;
}

.toast.error .toast-icon {
    color: #dc2626;
}

.toast.warning .toast-icon {
    color: #f59e0b;
}

.toast-message {
    flex: 1;
    font-size: 0.9rem;
    color: #374151;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-container {
        width: 95%;
        max-width: 1190px;
        height: auto;
        min-height: 850px;
        margin: 20px;
    }
    
    .upload-area {
        width: 90%;
        max-width: 600px;
        height: 350px;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: 20px;
    }
    
    .tool-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .tool-title {
        font-size: 1.5rem;
    }
    
    .upload-area {
        width: 100%;
        height: 300px;
    }
    
    .upload-content {
        padding: 40px 20px;
    }
    
    .controls-area {
        flex-direction: column;
        gap: 15px;
    }
    
    .rotation-controls {
        justify-content: center;
    }
    
    .action-controls {
        width: 100%;
        justify-content: center;
    }
    
    .pages-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        padding: 15px;
    }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Selection States */
.select-all-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    margin-bottom: 15px;
}

.select-all-btn:hover {
    background: #2563eb;
}

/* Page Status Indicators */
.page-item.processing {
    opacity: 0.6;
    pointer-events: none;
}

.page-item.processed {
    border-color: #10b981;
    background: #f0fdf4;
}